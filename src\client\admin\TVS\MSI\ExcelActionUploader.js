import React, { useState, useRef, useEffect } from 'react';
import * as XLSX from 'xlsx';
import { FileUpload } from 'primereact/fileupload';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { ScrollPanel } from 'primereact/scrollpanel';
import { Divider } from 'primereact/divider';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';

const ExcelActionUploader = ({ dealerList, assessorList }) => {
  const [rawRows, setRawRows] = useState([]);
  const [jsonData, setJsonData] = useState([]);
  const [uploadStatus, setUploadStatus] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const toast = useRef(null);



  const handleFileUpload = async (event) => {
    const file = event.files[0];
    if (!file) return;

    const reader = new FileReader();

    reader.onload = async (evt) => {
      try {
        const bstr = evt.target.result;
        const wb = XLSX.read(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];

        // Extract raw data with no header mapping
        const rawData = XLSX.utils.sheet_to_json(ws, { header: 1, defval: '' });
        const headers = rawData[1]; // Second row is the header
        const dataRows = rawData.slice(2); // Data starts from third row

        // Map rows using headers
        const data = dataRows.map(row => {
          const rowData = {};
          headers.forEach((header, index) => {
            rowData[header] = row[index] || '';
          });
          return rowData;
        });

        // Fill empty cells with last known value
        const lastKnown = {};
        const filledRows = data.map(row => {
          const filled = { ...row };
          for (const key in row) {
            if (row[key]) {
              lastKnown[key] = row[key];
            } else {
              filled[key] = lastKnown[key] || '';
            }
          }
          return filled;
        });

        setRawRows(filledRows);
        setJsonData([]);
        setUploadStatus('');

        toast.current.show({
          severity: 'success',
          summary: 'Excel Loaded',
          detail: `Loaded ${filledRows.length} rows. Review and convert.`,
        });

      } catch (error) {
        console.error('Error processing file:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to read the Excel file. Check the format.',
        });
      }

      event.options.clear();
    };

    reader.readAsBinaryString(file);
  };

  const convertRawToActionData = () => {
    if (rawRows.length === 0) return;

    const converted = rawRows
      .filter(row => row['MSI ID'] && row['Required Action'])
      .map(row => {
        const dealerCode = String(row['Dealer Code'] || '').trim();

        // Find dealer from dealerList using vendorData.code
        const dealer = dealerList.find(d => d.vendorData?.code === dealerCode);
        const dealerId = dealer?.vendorData?.id || null;

        const msiId = row['MSI ID'] || '';
        const msiPointNo = String(row['MSI Point no.'] || '').trim();
        const datePart = msiId?.split('-').pop();
        const submissionReferenceId = `MSI-${dealerCode}-${datePart}-${msiPointNo}`;

        return {
          applicationId: 'DAA',
          maskId: submissionReferenceId,
          application: 'DealerAssessmentAssignment',
          actionType: 'Checklist Submission',
          actionToBeTaken: row['Required Action'],
          applicationDetails: {
            personResponsible: row['Responsible Person'] || '',
            criteria: '',
            subCriteria: '',
          },
          uploads: [],
          dueDate: row['Target date to complete']
            ? new Date(row['Target date to complete']).toISOString()
            : '',
          assignedToId: dealerId ? [dealerId] : [],
          status: 'Initiated',
          description: msiPointNo,
          createdDate: new Date().toISOString(),
          created: new Date().toString(),
          updated: new Date().toString(),
          remarks: row['Observation Evidence'] || '',
          objectId: null,
          submittedBy: null,
          appId: 33,
          category:
            row['Sales / Service']?.toLowerCase() === 'sales'
              ? '1'
              : row['Sales / Service']?.toLowerCase() === 'service'
                ? '2'
                : '',
          vendorId: dealerId,
          trackId: crypto.randomUUID(),
        };
      });

    setJsonData(converted);

    toast.current.show({
      severity: 'info',
      summary: 'Converted',
      detail: `Converted ${converted.length} records to action format.`,
    });
  };

  const uploadActionsToAPI = async () => {
    if (jsonData.length === 0) {
      toast.current.show({
        severity: 'warn',
        summary: 'No Data',
        detail: 'Please convert data to action format first.',
      });
      return;
    }

    setIsUploading(true);
    setUploadStatus('Uploading...');

    try {
      const response = await APIServices.post(API.UploadChecklistActions, jsonData);

      setUploadStatus(`Upload successful: ${response.data.message || 'Actions uploaded successfully'}`);
      toast.current.show({
        severity: 'success',
        summary: 'Upload Complete',
        detail: `Successfully uploaded ${jsonData.length} action records.`,
      });

      setJsonData([]);
      setRawRows([]);
    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Upload failed';
      setUploadStatus(`Upload failed: ${errorMessage}`);
      toast.current.show({
        severity: 'error',
        summary: 'Upload Failed',
        detail: errorMessage,
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="p-6">
      <Toast ref={toast} />
      <Card>
        <div className="mb-4">
          <h2 className="text-xl font-bold mb-2">Upload MSI Checklist Excel</h2>
          <p className="text-gray-600">
            Upload an Excel file containing MSI checklist rows. First you'll preview raw data, then convert and upload it.
          </p>
        </div>

        <FileUpload
          name="file"
          customUpload
          uploadHandler={handleFileUpload}
          accept=".xlsx,.xls"
          mode="basic"
          auto
          chooseLabel="Choose Excel File"
          className="mb-3"
        />

        <Divider />

        <Panel header={`Raw Excel Data (${rawRows.length})`} toggleable>
          <ScrollPanel style={{ width: '100%', height: '300px' }}>
            {rawRows.length > 0 ? (
              <DataTable value={rawRows} scrollable scrollHeight="300px" size="small">
                {Object.keys(rawRows[0] || {}).map((key, i) => (
                  <Column key={i} field={key} header={key} style={{ minWidth: '150px' }} />
                ))}
              </DataTable>
            ) : (
              <div className="text-center text-gray-500 p-4">
                No data to display. Upload Excel to begin.
              </div>
            )}
          </ScrollPanel>

          {rawRows.length > 0 && (
            <div className="mt-3 flex justify-content-end">
              <Button
                label="Convert to Action Format"
                icon="pi pi-sync"
                className="p-button-info"
                onClick={convertRawToActionData}
              />
            </div>
          )}
        </Panel>

        {jsonData.length > 0 && (
          <Panel header={`Converted Action Data (${jsonData.length})`} toggleable className="mt-4">
            <ScrollPanel style={{ width: '100%', height: '300px' }}>
              <DataTable value={jsonData} scrollable scrollHeight="300px" size="small">
                {Object.keys(jsonData[0] || {}).map((key, i) => (
                  <Column key={i} field={key} header={key} style={{ minWidth: '150px' }} />
                ))}
              </DataTable>
            </ScrollPanel>

            <div className="flex justify-content-end mt-3">
              <Button
                label="Upload to API"
                icon="pi pi-upload"
                onClick={uploadActionsToAPI}
                className="p-button-success"
                loading={isUploading}
                disabled={jsonData.length === 0 || isUploading}
              />
            </div>
          </Panel>
        )}
      </Card>
    </div>
  );
};

export default ExcelActionUploader;
