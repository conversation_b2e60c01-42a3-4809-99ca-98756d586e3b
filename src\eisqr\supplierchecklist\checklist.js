import React, { useState, useEffect, useRef } from 'react';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dialog } from 'primereact/dialog';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';

import Swal from 'sweetalert2';
import APIServices from '../../service/APIService';
import { API } from '../../constants/api_url';
import './checklist.css';

const SupplierChecklist = () => {
    const [selected, setSelected] = useState({
        category: null,
        section: null,
        checklist: null
    });

    // Data states
    const [categories, setCategories] = useState([]);
    const [sections, setSections] = useState([]);
    const [checklists, setChecklists] = useState([]);
    const [filteredSections, setFilteredSections] = useState([]);
    const [filteredChecklists, setFilteredChecklists] = useState([]);

    // Loading states
    const [loading, setLoading] = useState({
        categories: false,
        sections: false,
        checklists: false
    });

    // Modal states
    const [showCurateModal, setShowCurateModal] = useState(false);
    const [curatingChecklist, setCuratingChecklist] = useState(null);

    const toast = useRef(null);

    // API service functions
    const fetchCategories = async () => {
        try {
            setLoading(prev => ({ ...prev, categories: true }));
            const response = await APIServices.get(API.SupplyCategories);
            setCategories(response.data || []);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load categories'
            });
        } finally {
            setLoading(prev => ({ ...prev, categories: false }));
        }
    };

    const fetchSections = async (categoryId) => {
        try {
            setLoading(prev => ({ ...prev, sections: true }));
            const response = await APIServices.get(API.SupplySections(categoryId));
            const sectionsData = response.data || [];
            setSections(sectionsData);
            setFilteredSections(sectionsData);
        } catch (error) {
            console.error('Error fetching sections:', error);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load sections'
            });
        } finally {
            setLoading(prev => ({ ...prev, sections: false }));
        }
    };

    const fetchChecklists = async (sectionId) => {
        try {
            setLoading(prev => ({ ...prev, checklists: true }));
            const response = await APIServices.get(API.SupplyChecklists(sectionId));
            const checklistsData = response.data || [];
            setChecklists(checklistsData);
            setFilteredChecklists(checklistsData);
        } catch (error) {
            console.error('Error fetching checklists:', error);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load checklists'
            });
        } finally {
            setLoading(prev => ({ ...prev, checklists: false }));
        }
    };

    // Load categories on component mount
    useEffect(() => {
        fetchCategories();
    }, []);

    // Update sections when category is selected
    useEffect(() => {
        if (selected.category) {
            fetchSections(selected.category);
            setSelected(prev => ({ ...prev, section: null, checklist: null }));
        } else {
            setFilteredSections([]);
            setSections([]);
        }
    }, [selected.category]);

    // Update checklists when section is selected
    useEffect(() => {
        if (selected.section) {
            fetchChecklists(selected.section);
            setSelected(prev => ({ ...prev, checklist: null }));
        } else {
            setFilteredChecklists([]);
            setChecklists([]);
        }
    }, [selected.section]);

    // Selection handlers
    const handleCategorySelect = (id) => {
        setSelected(prev => ({ ...prev, category: id }));
    };

    const handleSectionSelect = (id) => {
        setSelected(prev => ({ ...prev, section: id }));
    };

    const handleChecklistSelect = (id) => {
        setSelected(prev => ({ ...prev, checklist: id }));
    };

    // Add handlers
    const handleAddCategory = async (name) => {
        try {
            const response = await APIServices.post(API.SupplyCategories, { name });
            setCategories(prev => [...prev, response.data]);
            toast.current.show({ severity: 'success', summary: 'Success', detail: 'Category added successfully' });
        } catch (error) {
            console.error('Error adding category:', error);
            toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to add category' });
        }
    };

    const handleAddSection = async (name) => {
        if (!selected.category) return;
        try {
            const response = await APIServices.post(API.SupplySections(selected.category), { name });
            const newSection = response.data;
            setSections(prev => [...prev, newSection]);
            setFilteredSections(prev => [...prev, newSection]);
            toast.current.show({ severity: 'success', summary: 'Success', detail: 'Section added successfully' });
        } catch (error) {
            console.error('Error adding section:', error);
            toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to add section' });
        }
    };

    const handleAddChecklist = async (name) => {
        if (!selected.section) return;
        try {
            const response = await APIServices.post(API.SupplyChecklists(selected.section), { name });
            const newChecklist = response.data;
            setChecklists(prev => [...prev, newChecklist]);
            setFilteredChecklists(prev => [...prev, newChecklist]);
            toast.current.show({ severity: 'success', summary: 'Success', detail: 'Checklist added successfully' });
        } catch (error) {
            console.error('Error adding checklist:', error);
            toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to add checklist' });
        }
    };

    // Edit handlers
    const handleEditCategory = async (id, newName) => {
        try {
            await APIServices.put(API.SupplyCategories_Edit(id), { name: newName });
            setCategories(prev => prev.map(c => c.id === id ? { ...c, name: newName } : c));
            toast.current.show({ severity: 'success', summary: 'Success', detail: 'Category updated successfully' });
        } catch (error) {
            console.error('Error updating category:', error);
            toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to update category' });
        }
    };

    const handleEditSection = async (id, newName) => {
        try {
            await APIServices.put(API.SupplySections_Edit(id), { name: newName });
            setSections(prev => prev.map(s => s.id === id ? { ...s, name: newName } : s));
            setFilteredSections(prev => prev.map(s => s.id === id ? { ...s, name: newName } : s));
            toast.current.show({ severity: 'success', summary: 'Success', detail: 'Section updated successfully' });
        } catch (error) {
            console.error('Error updating section:', error);
            toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to update section' });
        }
    };

    const handleEditChecklist = async (id, newName) => {
        try {
            const payload = JSON.stringify({ name: newName });
            await APIServices.patch(API.SupplyChecklists_Edit(id), payload, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            setChecklists(prev => prev.map(c => c.id === id ? { ...c, name: newName } : c));
            setFilteredChecklists(prev => prev.map(c => c.id === id ? { ...c, name: newName } : c));
            toast.current.show({ severity: 'success', summary: 'Success', detail: 'Checklist updated successfully' });
        } catch (error) {
            console.error('Error updating checklist:', error);
            toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to update checklist' });
        }
    };

    // Delete handlers with confirmation
    const handleDeleteCategory = (id) => {
        const category = categories.find(c => c.id === id);
        Swal.fire({
            title: 'Are you sure?',
            text: `Do you want to delete the category "${category?.name}"? This will also delete all related sections and checklists.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    await APIServices.delete(API.SupplyCategories_Edit(id));
                    setCategories(prev => prev.filter(c => c.id !== id));
                    if (selected.category === id) {
                        setSelected({ category: null, section: null, checklist: null });
                        setFilteredSections([]);
                        setFilteredChecklists([]);
                        setSections([]);
                        setChecklists([]);
                    }
                    toast.current.show({ severity: 'success', summary: 'Deleted', detail: 'Category deleted successfully' });
                } catch (error) {
                    console.error('Error deleting category:', error);
                    toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to delete category' });
                }
            }
        });
    };

    const handleDeleteSection = (id) => {
        const section = sections.find(s => s.id === id);
        Swal.fire({
            title: 'Are you sure?',
            text: `Do you want to delete the section "${section?.name}"? This will also delete all related checklists.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    await APIServices.delete(API.SupplySections_Edit(id));
                    setSections(prev => prev.filter(s => s.id !== id));
                    setFilteredSections(prev => prev.filter(s => s.id !== id));
                    if (selected.section === id) {
                        setSelected(prev => ({ ...prev, section: null, checklist: null }));
                        setFilteredChecklists([]);
                        setChecklists([]);
                    }
                    toast.current.show({ severity: 'success', summary: 'Deleted', detail: 'Section deleted successfully' });
                } catch (error) {
                    console.error('Error deleting section:', error);
                    toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to delete section' });
                }
            }
        });
    };

    const handleDeleteChecklist = (id) => {
        const checklist = checklists.find(c => c.id === id);
        Swal.fire({
            title: 'Are you sure?',
            text: `Do you want to delete the checklist "${checklist?.name}"?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    await APIServices.delete(API.SupplyChecklists_Edit(id));
                    setChecklists(prev => prev.filter(c => c.id !== id));
                    setFilteredChecklists(prev => prev.filter(c => c.id !== id));
                    if (selected.checklist === id) {
                        setSelected(prev => ({ ...prev, checklist: null }));
                    }
                    toast.current.show({ severity: 'success', summary: 'Deleted', detail: 'Checklist deleted successfully' });
                } catch (error) {
                    console.error('Error deleting checklist:', error);
                    toast.current.show({ severity: 'error', summary: 'Error', detail: 'Failed to delete checklist' });
                }
            }
        });
    };

    // Curate handler - opens modal instead of navigating
    const handleCurate = (id) => {
        const checklist = checklists.find(ch => ch.id === id);
        if (checklist) {
            setCuratingChecklist(checklist);
            setShowCurateModal(true);
        }
    };

    const handleCloseCurateModal = () => {
        setShowCurateModal(false);
        setCuratingChecklist(null);
    };

    // Custom Column Widget Component
    const CustomColumnWidget = ({
        title,
        items,
        selectedId,
        onItemSelect,
        onAdd,
        onEdit,
        onDelete,
        onCurate,
        bgColor,
        borderColor,
        addButtonText,
        searchPlaceholder,
        disabled = false,
        showCurate = false,
        isLoading = false
    }) => {
        const [searchTerm, setSearchTerm] = useState('');
        const [isAdding, setIsAdding] = useState(false);
        const [newItemName, setNewItemName] = useState('');

        const filteredItems = items.filter(item =>
            item.name.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const handleAddClick = () => {
            setIsAdding(true);
            setNewItemName('');
        };

        const handleSaveNew = () => {
            if (newItemName.trim()) {
                onAdd(newItemName.trim());
                setIsAdding(false);
                setNewItemName('');
            }
        };

        const handleCancelNew = () => {
            setIsAdding(false);
            setNewItemName('');
        };

        const handleKeyPress = (e) => {
            if (e.key === 'Enter') {
                handleSaveNew();
            } else if (e.key === 'Escape') {
                handleCancelNew();
            }
        };

        return (
            <div className={`custom-column-widget ${bgColor} ${borderColor} ${disabled ? 'disabled' : ''}`}>
                {/* Header Section */}
                <div className="widget-header">
                    <h2 className="widget-title">{title}</h2>

                    {/* Search Box */}
                    <div className="search-box">
                        <i className="pi pi-search search-icon"></i>
                        <InputText
                            type="text"
                            placeholder={searchPlaceholder}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            disabled={disabled}
                            className="search-field"
                        />
                    </div>

                    {/* Add Section */}
                    {isAdding ? (
                        <div className="add-section">
                            <InputText
                                type="text"
                                value={newItemName}
                                onChange={(e) => setNewItemName(e.target.value)}
                                onKeyDown={handleKeyPress}
                                placeholder="Enter name..."
                                className="add-field"
                                autoFocus
                            />
                            <Button
                                icon="pi pi-check"
                                onClick={handleSaveNew}
                                className="p-button-success p-button-sm save-btn"
                                title="Save"
                            />
                            <Button
                                icon="pi pi-times"
                                onClick={handleCancelNew}
                                className="p-button-danger p-button-sm cancel-btn"
                                title="Cancel"
                            />
                        </div>
                    ) : (
                        <Button
                            icon="pi pi-plus"
                            label={addButtonText}
                            onClick={handleAddClick}
                            disabled={disabled}
                            className="add-btn"
                        />
                    )}
                </div>

                {/* Content Section */}
                <div className="widget-content">
                    {isLoading ? (
                        <div className="loading-message">
                            <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                            <p>Loading...</p>
                        </div>
                    ) : disabled ? (
                        <div className="empty-message">
                            <p>Select a parent item to view options</p>
                        </div>
                    ) : filteredItems.length === 0 && !isAdding ? (
                        <div className="empty-message">
                            {searchTerm ? (
                                <p>No items match your search</p>
                            ) : (
                                <p>No items available</p>
                            )}
                        </div>
                    ) : (
                        <div className="items-container">
                            {filteredItems.map((item) => (
                                <CustomItemCard
                                    key={item.id}
                                    item={item}
                                    isSelected={selectedId === item.id}
                                    onSelect={() => onItemSelect(item.id)}
                                    onEdit={onEdit}
                                    onDelete={() => onDelete(item.id)}
                                    onCurate={onCurate ? () => onCurate(item.id) : undefined}
                                    showCurate={showCurate}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        );
    };

    // Custom Item Card Component
    const CustomItemCard = ({ item, isSelected, onSelect, onEdit, onDelete, onCurate, showCurate = false }) => {
        const [isHovered, setIsHovered] = useState(false);
        const [isEditing, setIsEditing] = useState(false);
        const [editValue, setEditValue] = useState(item.name);

        const handleEditClick = (e) => {
            e.stopPropagation();
            setIsEditing(true);
            setEditValue(item.name);
        };

        const handleCurateClick = (e) => {
            e.stopPropagation();
            onCurate?.();
        };

        const handleSaveEdit = () => {
            if (editValue.trim()) {
                onEdit(item.id, editValue.trim());
                setIsEditing(false);
            }
        };

        const handleCancelEdit = () => {
            setIsEditing(false);
            setEditValue(item.name);
        };

        const handleKeyPress = (e) => {
            if (e.key === 'Enter') {
                handleSaveEdit();
            } else if (e.key === 'Escape') {
                handleCancelEdit();
            }
        };

        return (
            <div
                className={`custom-item-card ${isSelected ? 'selected' : ''} ${isHovered ? 'hovered' : ''}`}
                onClick={isEditing ? undefined : onSelect}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                {/* Card Content */}
                <div className={`card-main-content ${showCurate ? 'with-curate-space' : ''}`}>
                    {isEditing ? (
                        <div className="edit-section">
                            <InputText
                                type="text"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                onKeyDown={handleKeyPress}
                                className="edit-field"
                                autoFocus
                                onFocus={(e) => e.target.select()}
                            />
                            <Button
                                icon="pi pi-check"
                                onClick={handleSaveEdit}
                                className="p-button-success p-button-sm edit-save"
                                title="Save"
                            />
                            <Button
                                icon="pi pi-times"
                                onClick={handleCancelEdit}
                                className="p-button-danger p-button-sm edit-cancel"
                                title="Cancel"
                            />
                        </div>
                    ) : (
                        <h3 className="item-title">{item.name}</h3>
                    )}
                </div>

                {/* Action Buttons */}
                {!isEditing && (
                    <div className={`action-controls ${isHovered || isSelected ? 'visible' : ''}`}>
                        {showCurate && (
                            <Button
                                icon="pi pi-book"
                                onClick={handleCurateClick}
                                className="p-button-info p-button-sm action-control"
                                title="Curate Content"
                            />
                        )}

                        <Button
                            icon="pi pi-pencil"
                            onClick={handleEditClick}
                            className="p-button-secondary p-button-sm action-control"
                            title="Edit"
                        />

                        <Button
                            icon="pi pi-trash"
                            onClick={(e) => {
                                e.stopPropagation();
                                onDelete();
                            }}
                            className="p-button-danger p-button-sm action-control"
                            title="Delete"
                        />
                    </div>
                )}

                {/* Selection Indicator */}
                {isSelected && <div className="selection-bar" />}
            </div>
        );
    };

    // EditCheckpointDialog Component - adapted for PrimeReact
    const EditCheckpointDialog = ({ isOpen, onClose, onSave, initialData }) => {
        const [title, setTitle] = useState(initialData?.title || '');
        const [description, setDescription] = useState(initialData?.description || '');
        const [questions, setQuestions] = useState(
            initialData?.questions || [
                { id: '1', text: '', type: 'A', questionNumber: '1.1.1', numerator: 1, denominator: 1 },
                { id: '2', text: '', type: 'A', questionNumber: '1.1.2', numerator: 1, denominator: 1 }
            ]
        );

        const questionTypes = [
            { label: 'A (Yes/No/NA)', value: 'A' },
            { label: 'B (Yes/No)', value: 'B' },
            { label: 'C (% selector)', value: 'C' },
            { label: 'D (Description)', value: 'D' },
            { label: 'E (Attachment)', value: 'E' }
        ];

        const addQuestion = () => {
            const newQuestion = {
                id: Date.now().toString(),
                text: '',
                type: 'A',
                questionNumber: '',
                numerator: 1,
                denominator: 1
            };
            setQuestions(prev => [...prev, newQuestion]);
        };

        const removeQuestion = (id) => {
            setQuestions(prev => prev.filter(q => q.id !== id));
        };

        const updateQuestion = (id, field, value) => {
            setQuestions(prev => prev.map(q => {
                if (q.id === id) {
                    const updatedQuestion = { ...q, [field]: value };

                    if (field === 'type') {
                        if (value === 'C') {
                            delete updatedQuestion.numerator;
                            delete updatedQuestion.denominator;
                            updatedQuestion.marks = '';
                        } else if (value === 'D' || value === 'E') {
                            delete updatedQuestion.numerator;
                            delete updatedQuestion.denominator;
                            delete updatedQuestion.marks;
                        } else {
                            delete updatedQuestion.marks;
                            updatedQuestion.numerator = updatedQuestion.numerator || 1;
                            updatedQuestion.denominator = updatedQuestion.denominator || 1;
                        }
                    }

                    return updatedQuestion;
                }
                return q;
            }));
        };

        const handleSave = () => {
            // Validation: Check if at least one question is added
            if (questions.length === 0) {
                toast.current.show({
                    severity: 'warn',
                    summary: 'Validation Error',
                    detail: 'Please add at least one question before saving.'
                });
                return;
            }

            // Validation: Check if all questions have text
            const emptyQuestions = questions.filter(q => !q.text || q.text.trim() === '');
            if (emptyQuestions.length > 0) {
                toast.current.show({
                    severity: 'warn',
                    summary: 'Validation Error',
                    detail: 'Please fill in all question texts before saving.'
                });
                return;
            }

            onSave({
                title,
                description,
                questions,
                required: false // Always set to false since we're removing the required option
            });
        };

        return (
            <Dialog
                visible={isOpen}
                onHide={onClose}
                header="Edit Checkpoint Group"
                style={{ width: '50vw', maxHeight: '90vh' }}
                modal
                className="checkpoint-dialog"
            >
                <div className="checkpoint-dialog-content">
                    <div className="field">
                        <label htmlFor="checkpoint-title">Group Title</label>
                        <InputText
                            id="checkpoint-title"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            placeholder="Enter checkpoint group title"
                            className="w-full"
                        />
                    </div>

                    <div className="field">
                        <label htmlFor="checkpoint-description">Group Description (Optional)</label>
                        <InputTextarea
                            id="checkpoint-description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Enter group description or instructions"
                            rows={3}
                            className="w-full"
                        />
                    </div>

                    <div className="questions-section">
                        <div className="questions-header">
                            <label>Questions</label>
                            <Button
                                icon="pi pi-plus"
                                label="Add Question"
                                className="p-button-sm"
                                onClick={addQuestion}
                            />
                        </div>

                        {questions.map((question, index) => (
                            <div key={question.id} className="question-item">
                                <div className="question-header">
                                    <div className="question-number-field">
                                        <label>Question Number:</label>
                                        <InputText
                                            value={question.questionNumber}
                                            onChange={(e) => updateQuestion(question.id, 'questionNumber', e.target.value)}
                                            placeholder="e.g., 1.1.1"
                                            style={{ width: '80px' }}
                                        />
                                    </div>
                                    {questions.length > 1 && (
                                        <Button
                                            icon="pi pi-trash"
                                            className="p-button-danger p-button-text p-button-sm"
                                            onClick={() => removeQuestion(question.id)}
                                        />
                                    )}
                                </div>

                                <InputText
                                    value={question.text || ''}
                                    onChange={(e) => updateQuestion(question.id, 'text', e.target.value)}
                                    placeholder="Enter your question"
                                    className="w-full"
                                />

                                <div className="question-config">
                                    <div className="field">
                                        <label>Response Type</label>
                                        <Dropdown
                                            value={question.type}
                                            options={questionTypes}
                                            onChange={(e) => updateQuestion(question.id, 'type', e.value)}
                                            className="w-full"
                                        />
                                    </div>

                                    {(question.type === 'A' || question.type === 'B') && (
                                        <>
                                            <div className="field">
                                                <label>Numerator</label>
                                                <InputText
                                                    type="number"
                                                    value={question.numerator || 0}
                                                    onChange={(e) => updateQuestion(question.id, 'numerator', parseInt(e.target.value) || 0)}
                                                    min="0"
                                                />
                                            </div>
                                            <div className="field">
                                                <label>Denominator</label>
                                                <InputText
                                                    type="number"
                                                    value={question.denominator || 1}
                                                    onChange={(e) => updateQuestion(question.id, 'denominator', parseInt(e.target.value) || 1)}
                                                    min="1"
                                                />
                                            </div>
                                        </>
                                    )}

                                    {question.type === 'C' && (
                                        <div className="field marks-field">
                                            <label>Marks (Percentage-based)</label>
                                            <InputText
                                                value={question.marks || ''}
                                                onChange={(e) => updateQuestion(question.id, 'marks', e.target.value)}
                                                placeholder="e.g., 0% = 0, >50% = 1, >80% = 2"
                                                className="w-full"
                                            />
                                            <small>Examples: "0% = 0, &gt;50% = 1, &gt;80% = 2" or "5% = 1, 10% = 2"</small>
                                        </div>
                                    )}

                                    {(question.type === 'D' || question.type === 'E') && (
                                        <div className="field no-scoring">
                                            <small>No scoring required for this response type</small>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>



                    <div className="dialog-footer">
                        <Button
                            label="Cancel"
                            icon="pi pi-times"
                            className="p-button-outlined"
                            onClick={onClose}
                        />
                        <Button
                            label="Save Changes"
                            icon="pi pi-check"
                            onClick={handleSave}
                        />
                    </div>
                </div>
            </Dialog>
        );
    };

    // EditHeaderDialog Component - adapted for PrimeReact
    const EditHeaderDialog = ({ isOpen, onClose, onSave, initialData }) => {
        const [title, setTitle] = useState(initialData?.title || '');
        const [description, setDescription] = useState(initialData?.description || '');
        const [showPreview, setShowPreview] = useState(false);

        useEffect(() => {
            if (initialData) {
                setTitle(initialData.title || '');
                setDescription(initialData.description || '');
            }
        }, [initialData]);

        const handleSave = () => {
            if (title.trim()) {
                onSave({
                    title: title.trim(),
                    description: description.trim()
                });
                setShowPreview(true);
                setTimeout(() => {
                    setShowPreview(false);
                    onClose();
                }, 2000);
            }
        };

        const handleCancel = () => {
            setTitle(initialData?.title || '');
            setDescription(initialData?.description || '');
            setShowPreview(false);
            onClose();
        };

        return (
            <Dialog
                visible={isOpen}
                onHide={handleCancel}
                header="Edit Header"
                style={{ width: '400px' }}
                modal
                className="header-dialog"
            >
                {showPreview ? (
                    <div className="preview-content">
                        <div className="preview-success">
                            <i className="pi pi-check-circle" style={{ color: 'green', fontSize: '2rem' }}></i>
                            <h4>Header Updated!</h4>
                            <div className="preview-text">
                                <h5>Preview: {title}</h5>
                                {description && <p>{description}</p>}
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="header-dialog-content">
                        <div className="field">
                            <label htmlFor="header-title">Header Title *</label>
                            <InputText
                                id="header-title"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                placeholder="Enter header title"
                                className="w-full"
                                autoFocus
                            />
                        </div>

                        <div className="field">
                            <label htmlFor="header-description">Description (Optional)</label>
                            <InputTextarea
                                id="header-description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Enter header description or subtitle"
                                rows={3}
                                className="w-full"
                            />
                        </div>

                        <div className="dialog-footer">
                            <Button
                                label="Cancel"
                                icon="pi pi-times"
                                className="p-button-outlined"
                                onClick={handleCancel}
                            />
                            <Button
                                label="Save Header"
                                icon="pi pi-check"
                                onClick={handleSave}
                                disabled={!title.trim()}
                            />
                        </div>

                        <small className="keyboard-hint">
                            Press Ctrl+Enter to save, Escape to cancel
                        </small>
                    </div>
                )}
            </Dialog>
        );
    };

    // Custom Curation Modal Component - matching test.js design
    const CustomCurationModal = ({ isOpen, onClose, checklistData }) => {
        const [selectedComponents, setSelectedComponents] = useState([]);
        const [editingComponent, setEditingComponent] = useState(null);
        const [showEditCheckpointDialog, setShowEditCheckpointDialog] = useState(false);
        const [showEditHeaderDialog, setShowEditHeaderDialog] = useState(false);
        const [isSaving, setIsSaving] = useState(false);
        const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

        // Available components matching test.js
        const availableComponents = [
            { id: '1', type: 'Header', title: 'Header', description: 'Main page header', icon: 'H1' },
            { id: '10', type: 'Checkpoint Group', title: 'Checkpoint Group', description: 'Group of checkpoints', icon: '📋' },
        ];

        // Question types for checkpoint groups
        const questionTypes = [
            { label: 'Yes/No/NA', value: 'A' },
            { label: 'Yes/No', value: 'B' },
            { label: '% selector', value: 'C' },
            { label: 'Description', value: 'D' },
            { label: 'Attachment', value: 'E' }
        ];

        const addComponent = (component) => {
            const newComponent = {
                ...component,
                id: `${component.id}-${Date.now()}`,
                content: `No ${component.type.toLowerCase()} text added yet`,
                questions: component.type === 'Checkpoint Group' ? [] : undefined,
                required: false
            };
            setSelectedComponents(prev => [...prev, newComponent]);
            setHasUnsavedChanges(true);
        };

        const clearAllComponents = () => {
            if (selectedComponents.length === 0) return;

            Swal.fire({
                title: 'Clear All Components?',
                text: 'This will remove all components from the canvas. This action cannot be undone.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, clear all',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    setSelectedComponents([]);
                    setHasUnsavedChanges(false);
                    toast.current.show({
                        severity: 'info',
                        summary: 'Components Cleared',
                        detail: 'All components have been removed from the canvas'
                    });
                }
            });
        };

        const removeComponent = (id) => {
            setSelectedComponents(prev => prev.filter(comp => comp.id !== id));
            setHasUnsavedChanges(true);
        };

        const handleEditComponent = (component) => {
            setEditingComponent(component);
            if (component.type === 'Checkpoint Group') {
                setShowEditCheckpointDialog(true);
            } else if (component.type === 'Header') {
                setShowEditHeaderDialog(true);
            }
        };

        const handleSaveCheckpointComponent = (data) => {
            if (editingComponent) {
                setSelectedComponents(prev => prev.map(comp =>
                    comp.id === editingComponent.id
                        ? {
                            ...comp,
                            content: data.title,
                            questions: data.questions,
                            title: data.title,
                            required: data.required
                        }
                        : comp
                ));
                setHasUnsavedChanges(true);
            }
            setEditingComponent(null);
            setShowEditCheckpointDialog(false);
        };

        const handleSaveHeaderComponent = (data) => {
            if (editingComponent) {
                setSelectedComponents(prev => prev.map(comp =>
                    comp.id === editingComponent.id
                        ? {
                            ...comp,
                            content: data.title,
                            title: data.title,
                            description: data.description || comp.description
                        }
                        : comp
                ));
                setHasUnsavedChanges(true);
            }
            setEditingComponent(null);
            setShowEditHeaderDialog(false);
        };

        const getResponseTypeDisplay = (type) => {
            switch (type) {
                case 'A': return 'Yes/No/NA';
                case 'B': return 'Yes/No';
                case 'C': return '% selector';
                case 'D': return 'Description';
                case 'E': return 'Attachment';
                default: return type;
            }
        };

        const handleSaveCuration = async () => {
            if (selectedComponents.length === 0) {
                toast.current.show({
                    severity: 'warn',
                    summary: 'Nothing to Save',
                    detail: 'Please add some components before saving.'
                });
                return;
            }

            setIsSaving(true);

            // Create the curation data structure
            const curationData = {
                checklistId: checklistData?.id,
                checklistName: checklistData?.name,
                components: selectedComponents.map(comp => ({
                    id: comp.id,
                    type: comp.type,
                    title: comp.title || comp.content,
                    content: comp.content,
                    description: comp.description || '',
                    required: comp.required || false,
                    questions: comp.questions || [],
                    order: selectedComponents.indexOf(comp)
                })),
                lastModified: new Date().toISOString(),
                totalComponents: selectedComponents.length,
                totalQuestions: selectedComponents.reduce((total, comp) =>
                    total + (comp.questions ? comp.questions.length : 0), 0
                )
            };

            console.log(curationData);

            try {
                // Use API endpoint with PATCH method and stringify values
                const payload = JSON.stringify(curationData);
                await APIServices.patch(API.SupplyChecklists_Edit(checklistData?.id), { values: payload }, {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                setHasUnsavedChanges(false);

                toast.current.show({
                    severity: 'success',
                    summary: 'Curation Saved Successfully',
                    detail: `Saved ${selectedComponents.length} components with ${curationData.totalQuestions} questions for "${checklistData?.name}"`
                });

                // Close the modal after a brief delay to show the success message
                setTimeout(() => {
                    onClose();
                }, 2000);

            } catch (error) {
                console.error('Error saving curation:', error);
                toast.current.show({
                    severity: 'error',
                    summary: 'Save Failed',
                    detail: 'Failed to save curation. Please try again.'
                });
            } finally {
                setIsSaving(false);
            }
        };

        const handleCloseCuration = () => {
            if (selectedComponents.length > 0) {
                Swal.fire({
                    title: 'Unsaved Changes',
                    text: 'You have unsaved changes. Do you want to save before closing?',
                    icon: 'warning',
                    showCancelButton: true,
                    showDenyButton: true,
                    confirmButtonColor: '#3b82f6',
                    cancelButtonColor: '#6b7280',
                    denyButtonColor: '#dc2626',
                    confirmButtonText: 'Save & Close',
                    denyButtonText: 'Close without Saving',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        handleSaveCuration();
                    } else if (result.isDenied) {
                        onClose();
                    }
                    // If cancelled, do nothing (stay in modal)
                });
            } else {
                onClose();
            }
        };

        // Load existing curation data when modal opens
        useEffect(() => {
            const loadExistingCuration = async () => {
                if (isOpen && checklistData?.id) {
                    try {
                        // Fetch existing curation data from API
                        const response = await APIServices.get(API.SupplyChecklists_Edit(checklistData.id));

                        if (response.data && response.data.values) {
                            // Parse the values field which contains the stringified curation data
                            const curationData = JSON.parse(response.data.values);

                            if (curationData.components && curationData.components.length > 0) {
                                setSelectedComponents(curationData.components);
                                toast.current.show({
                                    severity: 'info',
                                    summary: 'Curation Loaded',
                                    detail: `Loaded ${curationData.components.length} existing components`
                                });
                            } else {
                                // No components in the curation data, start fresh
                                setSelectedComponents([]);
                            }
                        } else {
                            // No existing curation data, start fresh
                            setSelectedComponents([]);
                        }
                    } catch (error) {
                        console.warn('Failed to load existing curation:', error);
                        // If API call fails, start with empty components
                        setSelectedComponents([]);
                    }
                }
            };

            loadExistingCuration();
        }, [isOpen, checklistData?.id]);

        return (
            <Dialog
                visible={isOpen}
                onHide={handleCloseCuration}
                header={`Curate Content: ${checklistData?.name || 'Checklist'}`}
                style={{ width: '95vw', height: '90vh' }}
                maximizable
                modal
                className="curate-modal"
                closable={true}
                footer={
                    <div className="curate-footer">
                        <div className="footer-left">
                            <div className="footer-info">
                                <span className="component-count">
                                    {selectedComponents.length} component(s) configured
                                </span>
                                {selectedComponents.length > 0 && (
                                    <span className="question-count">
                                        • {selectedComponents.reduce((total, comp) =>
                                            total + (comp.questions ? comp.questions.length : 0), 0
                                        )} question(s)
                                    </span>
                                )}
                                {hasUnsavedChanges && (
                                    <span className="unsaved-indicator">
                                        • Unsaved changes
                                    </span>
                                )}
                            </div>
                            {selectedComponents.length > 0 && (
                                <Button
                                    label="Clear All"
                                    icon="pi pi-trash"
                                    className="p-button-outlined p-button-danger p-button-sm"
                                    onClick={clearAllComponents}
                                />
                            )}
                        </div>
                        <div className="footer-actions">
                            <Button
                                label="Close"
                                icon="pi pi-times"
                                className="p-button-outlined"
                                onClick={handleCloseCuration}
                            />
                            <Button
                                label={isSaving ? "Saving..." : "Save Curation"}
                                icon={isSaving ? "pi pi-spin pi-spinner" : "pi pi-save"}
                                onClick={handleSaveCuration}
                                disabled={selectedComponents.length === 0 || isSaving}
                                loading={isSaving}
                            />
                        </div>
                    </div>
                }
            >
                <div className="curate-modal-content">
                    {/* Left Sidebar - All Components */}
                    <div className="components-sidebar">
                        <div className="sidebar-header">
                            <div className="sidebar-title-section">
                                <h2 className="sidebar-title">All Components</h2>
                                <span className="components-count">
                                    {availableComponents.length} items
                                </span>
                            </div>
                            <p className="sidebar-description">
                                📌 Click components to add them to the canvas
                            </p>
                        </div>

                        <div className="components-list">
                            {availableComponents.map((component) => (
                                <div
                                    key={component.id}
                                    className="component-item"
                                    onClick={() => addComponent(component)}
                                >
                                    <div className="component-content">
                                        <div className="component-icon">
                                            {component.icon}
                                        </div>
                                        <div className="component-info">
                                            <h3 className="component-title">{component.title}</h3>
                                            <p className="component-description">{component.description}</p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Main Content Area - Canvas */}
                    <div className="canvas-area">
                        <div className="canvas-container">
                            <div className="canvas-content">
                                {selectedComponents.length === 0 ? (
                                    <div className="empty-canvas">
                                        <i className="pi pi-plus empty-icon"></i>
                                        <h3 className="empty-title">No components added yet</h3>
                                        <p className="empty-description">Click on components from the left panel to add them here</p>
                                    </div>
                                ) : (
                                    <div className="components-canvas">
                                        {selectedComponents.map((component, index) => (
                                            <div key={component.id} className="canvas-component">
                                                <div className="component-header">
                                                    <div className="component-meta">
                                                        <i className="pi pi-bars component-grip"></i>
                                                        <div className="component-type-icon">
                                                            {component.icon}
                                                        </div>
                                                    </div>

                                                    <div className="component-details">
                                                        <div className="component-title-row">
                                                            <span className="component-name">{component.title}</span>
                                                            <div className="component-badges">
                                                                <span className="content-badge">Content</span>
                                                                {component.type === 'Checkpoint Group' && component.required && (
                                                                    <span className="required-badge">Required</span>
                                                                )}
                                                            </div>
                                                        </div>

                                                        <div className="component-body">
                                                            <div className="component-text">{component.content}</div>

                                                            {component.type === 'Checkpoint Group' && component.questions && component.questions.length > 0 && (
                                                                <div className="questions-preview">
                                                                    <div className="questions-count">
                                                                        {component.questions.length} question(s) configured
                                                                    </div>

                                                                    {component.questions.map((question, qIndex) => (
                                                                        <div key={question.id || qIndex} className="question-preview">
                                                                            <div className="question-header">
                                                                                <span className="question-title">
                                                                                    Question {question.questionNumber || qIndex + 1}: {question.text || 'Untitled Question'}
                                                                                </span>
                                                                                <div className="question-meta">
                                                                                    <span className="question-type">
                                                                                        Type {question.type} - {getResponseTypeDisplay(question.type)}
                                                                                    </span>
                                                                                    {(question.type === 'A' || question.type === 'B') && (
                                                                                        <span className="question-score">
                                                                                            Score: {question.numerator || 0}/{question.denominator || 1}
                                                                                        </span>
                                                                                    )}
                                                                                </div>
                                                                            </div>

                                                                            <div className="question-preview-content">
                                                                                {question.type === 'A' && (
                                                                                    <div className="radio-preview">
                                                                                        <div className="radio-option">○ Yes</div>
                                                                                        <div className="radio-option">○ No</div>
                                                                                        <div className="radio-option">○ Not Applicable</div>
                                                                                    </div>
                                                                                )}

                                                                                {question.type === 'B' && (
                                                                                    <div className="radio-preview">
                                                                                        <div className="radio-option">○ Yes</div>
                                                                                        <div className="radio-option">○ No</div>
                                                                                    </div>
                                                                                )}

                                                                                {question.type === 'C' && (
                                                                                    <div className="preview-placeholder">
                                                                                        Percentage selector dropdown will appear here
                                                                                    </div>
                                                                                )}

                                                                                {question.type === 'D' && (
                                                                                    <div className="preview-placeholder">
                                                                                        Description text area will appear here
                                                                                    </div>
                                                                                )}

                                                                                {question.type === 'E' && (
                                                                                    <div className="preview-placeholder">
                                                                                        File attachment upload will appear here
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    <div className="component-actions">
                                                        <Button
                                                            icon="pi pi-pencil"
                                                            className="p-button-text p-button-sm"
                                                            onClick={() => handleEditComponent(component)}
                                                            title="Edit"
                                                        />
                                                        <Button
                                                            icon="pi pi-trash"
                                                            className="p-button-text p-button-sm p-button-danger"
                                                            onClick={() => removeComponent(component.id)}
                                                            title="Delete"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Edit Checkpoint Dialog */}
                <EditCheckpointDialog
                    isOpen={showEditCheckpointDialog}
                    onClose={() => {
                        setShowEditCheckpointDialog(false);
                        setEditingComponent(null);
                    }}
                    onSave={handleSaveCheckpointComponent}
                    initialData={editingComponent ? {
                        title: editingComponent.content || '',
                        description: '',
                        questions: editingComponent.questions || [],
                        required: false
                    } : undefined}
                />

                {/* Edit Header Dialog */}
                <EditHeaderDialog
                    isOpen={showEditHeaderDialog}
                    onClose={() => {
                        setShowEditHeaderDialog(false);
                        setEditingComponent(null);
                    }}
                    onSave={handleSaveHeaderComponent}
                    initialData={editingComponent ? {
                        title: editingComponent.content || '',
                        description: editingComponent.description || ''
                    } : undefined}
                />
            </Dialog>
        );
    };



    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
            <Toast ref={toast} />

            <div className="max-w-7xl mx-auto">
                <header className="mb-8">
                    <h1 className="text-3xl font-bold text-foreground mb-2">
                        Supplier Checklist Management
                    </h1>
                    <p className="text-muted-foreground text-lg">
                        Manage and organize your supplier compliance checklists across categories, sections, and individual items
                    </p>
                </header>

                <div style={{display:'grid'}} className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
                    {/* Categories Column */}
                    <CustomColumnWidget
                        title="Categories"
                        items={categories}
                        selectedId={selected.category}
                        onItemSelect={handleCategorySelect}
                        onAdd={handleAddCategory}
                        onEdit={handleEditCategory}
                        onDelete={handleDeleteCategory}
                        bgColor="bg-blue-50"
                        borderColor="border-blue-200"
                        addButtonText="Add Category"
                        searchPlaceholder="Search categories..."
                        isLoading={loading.categories}
                    />

                    {/* Sections Column */}
                    <CustomColumnWidget
                        title="Sections"
                        items={filteredSections}
                        selectedId={selected.section}
                        onItemSelect={handleSectionSelect}
                        onAdd={handleAddSection}
                        onEdit={handleEditSection}
                        onDelete={handleDeleteSection}
                        bgColor="bg-green-50"
                        borderColor="border-green-200"
                        addButtonText="Add Section"
                        searchPlaceholder="Search sections..."
                        disabled={!selected.category}
                        isLoading={loading.sections}
                    />

                    {/* Checklists Column */}
                    <CustomColumnWidget
                        title="Checklists"
                        items={filteredChecklists}
                        selectedId={selected.checklist}
                        onItemSelect={handleChecklistSelect}
                        onAdd={handleAddChecklist}
                        onEdit={handleEditChecklist}
                        onDelete={handleDeleteChecklist}
                        onCurate={handleCurate}
                        bgColor="bg-amber-50"
                        borderColor="border-amber-200"
                        addButtonText="Add Checklist"
                        searchPlaceholder="Search checklists..."
                        disabled={!selected.section}
                        showCurate={true}
                        isLoading={loading.checklists}
                    />
                </div>
            </div>

            {/* Curation Modal */}
            <CustomCurationModal
                isOpen={showCurateModal}
                onClose={handleCloseCurateModal}
                checklistData={curatingChecklist}
            />
        </div>
    );
};

export default SupplierChecklist;