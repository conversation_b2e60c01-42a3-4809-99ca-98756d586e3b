# Excel Action Uploader Component

## Overview
The `ExcelActionUploader` component allows users to upload Excel files containing checklist actions data. The component processes the Excel file, converts it to JSON format, and uploads it to the backend API.

## Features
- **File Upload**: Supports `.xlsx` and `.xls` file formats
- **Data Preview**: Shows a preview of the processed data in a table format
- **API Integration**: Uses the configured API endpoint for uploading data
- **Error Handling**: Provides user feedback for successful uploads and errors
- **Loading States**: Shows loading indicators during upload process

## Usage

### In Dashboard Component
The component is integrated into the MSI Dashboard as a new tab:

```jsx
<TabPanel header="Upload Actions">
    <ExcelActionUploader
        dealerList={transformSupplierData(dealerList)}
        assessorList={[...tvsExtUserList, ...userList]}
    />
</TabPanel>
```

### Props
- `dealerList`: Array of transformed dealer data
- `assessorList`: Array of assessor/user data

## API Configuration

The component uses the API endpoint defined in `src/constants/api_url.js`:

```javascript
UploadChecklistActions: baseurl + 'upload-checklist-actions-json',
```

## Excel File Format
The Excel file should contain columns with action data. The component will:
1. Read the first sheet of the Excel file
2. Convert it to JSON using the first row as headers
3. Display a preview of the data
4. Allow the user to upload the processed data

## Error Handling
- File processing errors are displayed to the user
- API upload errors show detailed error messages
- Success messages confirm successful uploads

## Dependencies
- `primereact/fileupload`: For file upload functionality
- `primereact/datatable`: For data preview
- `primereact/toast`: For user notifications
- `xlsx`: For Excel file processing
- `APIServices`: For API calls

## Example Excel Structure
```
| Action ID | Description | Priority | Status | Assigned To |
|-----------|-------------|----------|--------|-------------|
| ACT001    | Review docs | High     | Open   | John Doe    |
| ACT002    | Update code | Medium   | Closed | Jane Smith  |
```

The component will automatically detect column headers and process the data accordingly.
